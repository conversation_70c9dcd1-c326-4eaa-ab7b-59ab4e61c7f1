import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Only create Supabase client if credentials are provided
let supabase = null;
if (supabaseUrl && supabaseKey && !supabaseUrl.includes('https://vvwnzkpscijvpbiobykh.supabase.co')) {
  supabase = createClient(supabaseUrl, supabaseKey);
}

// Website configuration
const SITE_URL = process.env.SITE_URL || 'https://www.shebosburger.com';
const OUTPUT_PATH = path.join(process.cwd(), 'public', 'sitemap.xml');

// Static routes configuration
const staticRoutes = [
  {
    url: '/',
    changefreq: 'weekly',
    priority: '1.0'
  },
  {
    url: '/menu',
    changefreq: 'weekly',
    priority: '0.8'
  },
  {
    url: '/blog',
    changefreq: 'daily',
    priority: '0.8'
  },
  {
    url: '/privacy-policy',
    changefreq: 'monthly',
    priority: '0.3'
  },
  {
    url: '/terms-of-service',
    changefreq: 'monthly',
    priority: '0.3'
  }
];

async function generateSitemap() {
  try {
    console.log('🚀 Generating sitemap...');

    // Fetch published blog posts
    let blogPosts = null;
    let error = null;

    if (supabase) {
      try {
        const result = await supabase
          .from('blog_posts')
          .select('id, updated_at')
          .eq('published', true)
          .order('updated_at', { ascending: false });

        blogPosts = result.data;
        error = result.error;
      } catch (err) {
        error = err;
      }
    } else {
      console.log('⚠️  Supabase not configured - generating static sitemap only');
    }

    if (error) {
      console.warn('⚠️  Could not fetch blog posts:', error.message);
      console.log('📝 Generating sitemap with static routes only...');
    }

    // Generate XML
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Add static routes
    staticRoutes.forEach(route => {
      const lastmod = new Date().toISOString().split('T')[0];
      xml += `
  <url>
    <loc>${SITE_URL}${route.url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${route.changefreq}</changefreq>
    <priority>${route.priority}</priority>
  </url>`;
    });

    // Add blog posts if available
    if (blogPosts && blogPosts.length > 0) {
      console.log(`📰 Adding ${blogPosts.length} blog posts to sitemap...`);

      blogPosts.forEach(post => {
        const lastmod = new Date(post.updated_at).toISOString().split('T')[0];
        xml += `
  <url>
    <loc>${SITE_URL}/blog/${post.id}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>`;
      });
    }

    xml += `
</urlset>`;

    // Write sitemap to file
    fs.writeFileSync(OUTPUT_PATH, xml);

    console.log('✅ Sitemap generated successfully!');
    console.log(`📍 Location: ${OUTPUT_PATH}`);
    console.log(`🔗 URL: ${SITE_URL}/sitemap.xml`);

  } catch (error) {
    console.error('❌ Error generating sitemap:', error);
    process.exit(1);
  }
}

// Run the generator
generateSitemap();
