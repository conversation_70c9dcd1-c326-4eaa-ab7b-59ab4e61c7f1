import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ChevronRight, Star, MapPin, Phone, Mail } from 'lucide-react';
import { supabase, MenuItem, ContentSection } from '../lib/supabaseClient';
import { useLanguage } from '../contexts/LanguageContext';
import SEO from '../components/SEO';
import LazyImage from '../components/LazyImage';

const Home = () => {
  const { language, t } = useLanguage();
  const [featuredItems, setFeaturedItems] = useState<MenuItem[]>([]);
  const [content, setContent] = useState<Record<string, ContentSection>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      // Fetch content sections (both home and global)
      const { data: contentData, error: contentError } = await supabase
        .from('content')
        .select('*')
        .in('page', ['home', 'global'])
        .order('display_order');

      if (contentError) {
        console.error('Error fetching content:', contentError);
      } else if (contentData) {
        // Convert to record with name as key for easier access
        const contentMap: Record<string, ContentSection> = {};
        contentData.forEach((item: ContentSection) => {
          // Use name for contact items, section for others
          const key = item.name || item.section;
          contentMap[key] = item;
        });
        setContent(contentMap);
      }

      // Fetch featured menu items
      const { data: menuData, error: menuError } = await supabase
        .from('menu_items')
        .select('*')
        .eq('is_available', true)
        .limit(4);

      if (menuError) {
        console.error('Error fetching menu items:', menuError);
      } else {
        setFeaturedItems(menuData || []);
      }

      setLoading(false);
    };

    fetchData();
  }, []);

  // If no content is loaded from database, use default content
  const heroContent = content.hero || {
    title: t('hero.title'),
    content: t('hero.subtitle'),
    image_url: "https://images.pexels.com/photos/1639562/pexels-photo-1639562.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
  };

  const aboutContent = content.about || {
    title: t('about.title'),
    content: t('about.content'),
    image_url: "https://images.pexels.com/photos/2983101/pexels-photo-2983101.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
  };

  return (
    <div className="pt-16">
      <SEO
        title="Shebo's Sandwich & Burger - Handcrafted Burgers & Fresh Coffee"
        description="Experience the best handcrafted burgers made with premium ingredients. Shebo's Burger offers an unforgettable dining experience with fresh coffee and delicious food."
        keywords="burgers, restaurant, coffee, food, dining, handcrafted, fresh ingredients, cafe, local restaurant"
        image={heroContent.image_url}
      />

      {/* Hero Section */}
      <section
        className="relative h-[90vh] flex items-center justify-center bg-cover bg-center"
        style={{
          backgroundImage: `linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url(${heroContent.image_url})`
        }}
      >
        <div className="container-custom text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-6xl font-serif font-bold mb-4 text-white">
              {heroContent.title}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-2xl mx-auto">
              {heroContent.content}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/menu" className="btn bg-coffee hover:bg-coffee/90 text-cream">
                {t('hero.cta')}
              </Link>
              <a href="#about" className="btn bg-transparent border-2 border-white hover:bg-white/20">
                {t('about.title')}
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="section-padding bg-cream">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-serif font-bold mb-6">{aboutContent.title}</h2>
              <p className="text-lg mb-8">{aboutContent.content}</p>
              <div className="flex gap-4">
                <Link to="/menu" className="btn btn-primary">
                  {t('hero.cta')}
                </Link>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="rounded-lg overflow-hidden shadow-xl"
            >
              <LazyImage
                src={aboutContent.image_url}
                alt="About Shebo's Burger"
                className="w-full h-[400px]"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Featured Menu Items */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-serif font-bold mb-4">{t('menu.featured')}</h2>
            <p className="text-lg max-w-2xl mx-auto">
              {t('hero.subtitle')}
            </p>
          </div>

          {loading ? (
            <div className="text-center py-12">{t('common.loading')}</div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {featuredItems.length > 0 ? (
                  featuredItems.map((item) => (
                    <FeaturedMenuItem key={item.id} item={item} />
                  ))
                ) : (
                  // Fallback items if no data from database
                  <>
                    <FeaturedMenuItem
                      item={{
                        id: '1',
                        name: 'Classic Cheeseburger',
                        description: 'Juicy beef patty with melted cheddar, lettuce, tomato, and special sauce',
                        price: 12.99,
                        category: 'Burgers',
                        menu_type: 'restaurant',
                        image_url: 'https://images.pexels.com/photos/1633578/pexels-photo-1633578.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                        is_available: true,
                        created_at: ''
                      }}
                    />
                    <FeaturedMenuItem
                      item={{
                        id: '2',
                        name: 'Truffle Fries',
                        description: 'Crispy fries tossed with truffle oil, parmesan cheese, and fresh herbs',
                        price: 8.99,
                        category: 'Sides',
                        menu_type: 'restaurant',
                        image_url: 'https://images.pexels.com/photos/1893555/pexels-photo-1893555.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                        is_available: true,
                        created_at: ''
                      }}
                    />
                    <FeaturedMenuItem
                      item={{
                        id: '3',
                        name: 'BBQ Bacon Burger',
                        description: 'Smoky beef patty with crispy bacon, cheddar, and tangy BBQ sauce',
                        price: 14.99,
                        category: 'Burgers',
                        menu_type: 'restaurant',
                        image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                        is_available: true,
                        created_at: ''
                      }}
                    />
                    <FeaturedMenuItem
                      item={{
                        id: '4',
                        name: 'Oreo Milkshake',
                        description: 'Creamy vanilla milkshake blended with Oreo cookies and topped with whipped cream',
                        price: 6.99,
                        category: 'Drinks',
                        menu_type: 'cafe',
                        image_url: 'https://images.pexels.com/photos/3727250/pexels-photo-3727250.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                        is_available: true,
                        created_at: ''
                      }}
                    />
                  </>
                )}
              </div>

              <div className="text-center mt-12">
                <Link to="/menu" className="btn btn-primary inline-flex items-center">
                  {t('hero.cta')}
                  <ChevronRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </>
          )}
        </div>
      </section>

      {/* Testimonials */}
      <section className="section-padding bg-mocha text-cream">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-serif font-bold mb-4 text-cream">{t('testimonials.title')}</h2>
            <p className="text-lg max-w-2xl mx-auto">
              {t('hero.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <TestimonialCard
              name={t('testimonials.1.author')}
              text={t('testimonials.1.text')}
              rating={5}
            />
            <TestimonialCard
              name={t('testimonials.2.author')}
              text={t('testimonials.2.text')}
              rating={5}
            />
            <TestimonialCard
              name={t('testimonials.3.author')}
              text={t('testimonials.3.text')}
              rating={4}
            />
          </div>
        </div>
      </section>

      {/* Location & Contact */}
      <section className="section-padding bg-cream">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-serif font-bold mb-6">{content.contact?.title || t('contact.title')}</h2>
              <p className="text-lg mb-6">
                {content.contact?.content || t('hero.subtitle')}
              </p>

              <div className="space-y-4 mb-8">
                <div className="flex items-start">
                  <div className="bg-coffee text-cream p-2 rounded-md mr-4">
                    <MapPin className="h-6 w-6" />
                  </div>
                  <div>
                    <h4 className="font-bold">{t('contact.address')}</h4>
                    <p>{content['footer-address']?.content || 'Adres'}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-coffee text-cream p-2 rounded-md mr-4">
                    <Phone className="h-6 w-6" />
                  </div>
                  <div>
                    <h4 className="font-bold">{t('contact.phone')}</h4>
                    <p>{content['footer-phone']?.content || 'Telefon'}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-coffee text-cream p-2 rounded-md mr-4">
                    <Mail className="h-6 w-6" />
                  </div>
                  <div>
                    <h4 className="font-bold">{t('contact.email')}</h4>
                    <p>{content['footer-email']?.content || 'E-mail'}</p>
                  </div>
                </div>

                <div className="mt-6">
                  <h4 className="font-bold mb-2">{t('contact.hours')}</h4>
                  <p className="text-sm">{content['footer-hours']?.content || 'Pazartesi - Pazar: 11:30 - 21:45'}</p>
                </div>
              </div>

              <a
                href="https://maps.google.com"
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-primary"
              >
                {t('google.cta')}
              </a>
            </div>

            <div className="rounded-lg overflow-hidden h-[400px] shadow-lg">
              {/* Google Maps Embed */}
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3124.0156511162445!2d27.087404777190425!3d38.464199972146496!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14bbd9f28cc56d15%3A0x652d47ed2c99fa2e!2sShebo&#39;s%20Sandwich%20%26%20Burger%20%7C%20Hamburger%20Restoran%C4%B1!5e0!3m2!1sen!2sus!4v1748384383797!5m2!1sen!2sus"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Shebo's Sandwich & Burger"
              ></iframe>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

// Featured Menu Item Component
const FeaturedMenuItem = ({ item }: { item: MenuItem }) => {
  const { language, t } = useLanguage();
  const name = language === 'tr' ? item.name : (item.name_en || item.name);
  const description = language === 'tr' ? item.description : (item.description_en || item.description);

  const getPrice = () => {
    if (item.price) {
      return `${t('menu.currency')}${item.price}`;
    } else if (item.price_small) {
      return `${t('menu.currency')}${item.price_small}`;
    }
    return '';
  };

  return (
    <motion.div
      className="menu-item group"
      whileHover={{ y: -5 }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.4 }}
    >
      <div className="h-48 overflow-hidden">
        <LazyImage
          src={item.image_url || 'https://images.pexels.com/photos/1633578/pexels-photo-1633578.jpeg'}
          alt={name}
          className="w-full h-full transition-transform group-hover:scale-105"
        />
      </div>
      <div className="p-4">
        <div className="flex justify-between items-start">
          <h3 className="font-bold text-xl">{name}</h3>
          <span className="text-lg font-bold text-coffee">{getPrice()}</span>
        </div>
        {description && (
          <p className="text-gray-600 mt-2 line-clamp-2">{description}</p>
        )}
      </div>
    </motion.div>
  );
};

// Testimonial Card Component
const TestimonialCard = ({
  name,
  text,
  rating
}: {
  name: string;
  text: string;
  rating: number
}) => (
  <motion.div
    className="bg-white text-coffee p-6 rounded-lg shadow-md"
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.4 }}
  >
    <div className="flex mb-4">
      {[...Array(5)].map((_, i) => (
        <Star
          key={i}
          className={`h-5 w-5 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
        />
      ))}
    </div>
    <p className="mb-4 italic">"{text}"</p>
    <p className="font-bold">{name}</p>
  </motion.div>
);

export default Home;